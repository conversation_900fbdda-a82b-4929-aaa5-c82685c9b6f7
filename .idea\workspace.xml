<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="a874d56a-b0a0-4a0f-a8e3-0a93ccf83845" name="Changes" comment="creating github issues">
      <change beforePath="$PROJECT_DIR$/.github/workflows/deploy-from-cicd.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/.github/workflows/deploy-from-cicd.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/AugmentWebviewStateStore.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/AugmentWebviewStateStore.xml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitHubPullRequestSearchHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPEN&quot;,
    &quot;assignee&quot;: &quot;Saipriya104&quot;
  }
}</component>
  <component name="GithubPullRequestsUISettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;url&quot;: &quot;https://github.com/ChidhagniConsulting/gitops-argocd-apps.git&quot;,
    &quot;accountId&quot;: &quot;60b56d46-e190-4bcb-83ba-38ab3100d9e8&quot;
  }
}</component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2zpbmb5KDDAzaQ6XnXcW8YJIhR8" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;multi-stage-deployment-implementation&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/OneDrive/Documents/gitops-argocd-apps&quot;
  }
}</component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="a874d56a-b0a0-4a0f-a8e3-0a93ccf83845" name="Changes" comment="" />
      <created>1752431121781</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752431121781</updated>
    </task>
    <task id="LOCAL-00001" summary="creating github issues">
      <option name="closed" value="true" />
      <created>1752488390447</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1752488390447</updated>
    </task>
    <task id="LOCAL-00002" summary="creating github issues">
      <option name="closed" value="true" />
      <created>1752488752432</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1752488752432</updated>
    </task>
    <option name="localTasksCounter" value="3" />
    <servers />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="creating github issues" />
    <option name="LAST_COMMIT_MESSAGE" value="creating github issues" />
  </component>
</project>